import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Container, Row, <PERSON>, <PERSON>, Badge, <PERSON><PERSON>, Al<PERSON>, Spinner, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faTruck, 
  faArrowLeft, 
  faCalendar, 
  faMapMarkerAlt, 
  faUser, 
  faBoxes,
  faExclamationTriangle,
  faEdit,
  faCheck,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';

const DeliveryNoteDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  
  const [deliveryNote, setDeliveryNote] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDeliveryNote();
  }, [id]);

  const loadDeliveryNote = async () => {
    try {
      setLoading(true);
      const response = await procurementAPI.getDeliveryNote(id);
      setDeliveryNote(response.data);
    } catch (err) {
      console.error('Error loading delivery note:', err);
      setError(err.response?.data?.message || 'Failed to load delivery note');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft': return 'secondary';
      case 'pending': return 'warning';
      case 'dispatched': return 'info';
      case 'delivered': return 'success';
      case 'received': return 'success';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  };

  const getDeliveryTypeBadgeVariant = (type) => {
    switch (type?.toLowerCase()) {
      case 'purchase_order': return 'primary';
      case 'transfer': return 'info';
      case 'return': return 'warning';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading delivery note details...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/deliveries')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Delivery Notes
        </Button>
      </Container>
    );
  }

  if (!deliveryNote) {
    return (
      <Container fluid className="py-4">
        <Alert variant="warning">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          Delivery note not found
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/deliveries')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Delivery Notes
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Button 
            variant="outline-primary" 
            onClick={() => navigate('/procurement/deliveries')}
            className="mb-2"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to Delivery Notes
          </Button>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
            Delivery Note Details
          </h2>
          <p className="text-muted mb-0">View delivery note information and items</p>
        </div>
        <div>
          <Badge bg={getStatusBadgeVariant(deliveryNote.status)} className="fs-6 me-2">
            {deliveryNote.status?.toUpperCase()}
          </Badge>
          <Badge bg={getDeliveryTypeBadgeVariant(deliveryNote.delivery_type)} className="fs-6">
            {deliveryNote.delivery_type?.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </div>

      <Row>
        {/* Delivery Note Information */}
        <Col lg={8} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faTruck} className="me-2" />
                Delivery Information
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Delivery Number</label>
                    <div className="fw-semibold">{deliveryNote.delivery_number}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Delivery Date
                    </label>
                    <div>{new Date(deliveryNote.delivery_date).toLocaleDateString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Delivery Type</label>
                    <div>
                      <Badge bg={getDeliveryTypeBadgeVariant(deliveryNote.delivery_type)}>
                        {deliveryNote.delivery_type?.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Status</label>
                    <div>
                      <Badge bg={getStatusBadgeVariant(deliveryNote.status)}>
                        {deliveryNote.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                      Delivery Address
                    </label>
                    <div>{deliveryNote.delivery_address || 'Not specified'}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Total Amount</label>
                    <div className="fw-semibold text-success">
                      ₹{parseFloat(deliveryNote.total_amount || 0).toLocaleString()}
                    </div>
                  </div>
                </Col>
              </Row>
              
              {deliveryNote.notes && (
                <div className="mt-3">
                  <label className="form-label text-muted">Notes</label>
                  <div className="bg-light p-3 rounded">{deliveryNote.notes}</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Summary Card */}
        <Col lg={4} className="mb-4">
          <Card>
            <Card.Header className="text-primary">
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faBoxes} className="me-2" />
                Summary
              </h5>
            </Card.Header>
            <Card.Body style={{ backgroundColor: '#f8f9fa' }}>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Items:</span>
                <span className="fw-semibold">{deliveryNote.items?.length || 0}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Quantity:</span>
                <span className="fw-semibold">
                  {deliveryNote.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0}
                </span>
              </div>
              <hr />
              <div className="d-flex justify-content-between">
                <span>Total Amount:</span>
                <span className="fw-semibold text-success">
                  ₹{parseFloat(deliveryNote.total_amount || 0).toLocaleString()}
                </span>
              </div>
              
              {deliveryNote.created_at && (
                <div className="mt-3 pt-3 border-top">
                  <small className="text-muted">
                    <FontAwesomeIcon icon={faUser} className="me-1" />
                    Created: {new Date(deliveryNote.created_at).toLocaleString()}
                  </small>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Delivery Items */}
      <Card>
        <Card.Header className="text-primary">
          <h5 className="mb-0">
            <FontAwesomeIcon icon={faBoxes} className="me-2" />
            Delivery Items
          </h5>
        </Card.Header>
        <Card.Body className="p-0" style={{ backgroundColor: '#f8f9fa' }}>
          {deliveryNote.items && deliveryNote.items.length > 0 ? (
            <div className="table-responsive">
              <Table striped hover className="mb-0">
                <thead className="table-dark">
                  <tr>
                    <th>Item Name</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit</th>
                    <th>Unit Price</th>
                    <th>Total Price</th>
                  </tr>
                </thead>
                <tbody>
                  {deliveryNote.items.map((item, index) => (
                    <tr key={item.id || index}>
                      <td className="fw-semibold">{item.item_name}</td>
                      <td>{item.item_description || '-'}</td>
                      <td>{item.quantity}</td>
                      <td>{item.unit}</td>
                      <td>₹{parseFloat(item.unit_price || 0).toFixed(2)}</td>
                      <td className="fw-semibold">₹{parseFloat(item.total_price || 0).toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faBoxes} className="text-muted fa-2x mb-3" />
              <p className="text-muted">No items found for this delivery note</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default DeliveryNoteDetail;
