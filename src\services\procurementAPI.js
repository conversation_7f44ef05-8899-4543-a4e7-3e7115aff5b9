import api from './api';

const procurementAPI = {
  // Dashboard
  getDashboard: () => api.get('/procurement/dashboard'),

  // Purchase Requests
  getPurchaseRequests: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/procurement/purchase-requests?${queryParams.toString()}`);
  },

  getPurchaseRequest: (id) => api.get(`/procurement/purchase-requests/${id}`),

  createPurchaseRequest: (data) => api.post('/procurement/purchase-requests', data),

  updatePurchaseRequest: (id, data) => api.put(`/procurement/purchase-requests/${id}`, data),

  submitPurchaseRequest: (id) => api.post(`/procurement/purchase-requests/${id}/submit`),

  approvePurchaseRequest: (id, data) => api.post(`/procurement/purchase-requests/${id}/approve`, data),

  rejectPurchaseRequest: (id, data) => api.post(`/procurement/purchase-requests/${id}/reject`, data),

  // Proforma Invoices
  getProformaInvoices: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/procurement/proforma-invoices?${queryParams.toString()}`);
  },

  getProformaInvoice: (id) => api.get(`/procurement/proforma-invoices/${id}`),

  // Purchase Orders
  getPurchaseOrders: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/procurement/purchase-orders?${queryParams.toString()}`);
  },

  getPurchaseOrder: (id) => api.get(`/procurement/purchase-orders/${id}`),

  sendPurchaseOrder: (id) => api.post(`/procurement/purchase-orders/${id}/send`),

  receivePurchaseOrderItems: (id, data) => api.post(`/procurement/purchase-orders/${id}/receive`, data),

  // Delivery Notes
  getDeliveryNotes: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/procurement/delivery-notes?${queryParams.toString()}`);
  },

  getDeliveryNote: (id) => api.get(`/procurement/delivery-notes/${id}`),

  createDeliveryNote: (data) => api.post('/procurement/delivery-notes', data),

  dispatchDeliveryNote: (id, data) => api.post(`/procurement/delivery-notes/${id}/dispatch`, data),

  confirmDelivery: (id, data) => api.post(`/procurement/delivery-notes/${id}/confirm`, data),

  // Payment Transactions
  getPaymentTransactions: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/procurement/payment-transactions?${queryParams.toString()}`);
  },

  createPaymentTransaction: (data) => api.post('/procurement/payment-transactions', data),

  // Inventory Transfers
  getInventoryTransfers: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/procurement/inventory-transfers?${queryParams.toString()}`);
  },

  getInventoryTransfer: (id) => api.get(`/procurement/inventory-transfers/${id}`),

  // Suppliers (from existing inventory API)
  getSuppliers: () => api.get('/suppliers'),

  // Inventory items (from existing inventory API)
  getInventoryItems: (params = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/inventory?${queryParams.toString()}`);
  }
};

export default procurementAPI;
