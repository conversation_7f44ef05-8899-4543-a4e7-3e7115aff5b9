import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Container, Row, Col, Card, Badge, <PERSON><PERSON>, Alert, Spinner, Table } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFileInvoiceDollar, 
  faArrowLeft, 
  faCalendar, 
  faUser, 
  faBoxes,
  faExclamationTriangle,
  faEdit,
  faCheck,
  faTimes,
  faBuilding,
  faCreditCard,
  faClock
} from '@fortawesome/free-solid-svg-icons';
import procurementAPI from '../../services/procurementAPI';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../context/PermissionContext';

const ProformaInvoiceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { hasModuleAccess } = usePermissions();
  
  const [proformaInvoice, setProformaInvoice] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadProformaInvoice();
  }, [id]);

  const loadProformaInvoice = async () => {
    try {
      setLoading(true);
      const response = await procurementAPI.getProformaInvoice(id);
      setProformaInvoice(response.data);
    } catch (err) {
      console.error('Error loading proforma invoice:', err);
      setError(err.response?.data?.message || 'Failed to load proforma invoice');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft': return 'secondary';
      case 'sent': return 'warning';
      case 'accepted': return 'success';
      case 'rejected': return 'danger';
      case 'expired': return 'dark';
      default: return 'secondary';
    }
  };

  const getPaymentTermsBadgeVariant = (terms) => {
    switch (terms?.toLowerCase()) {
      case 'cash': return 'success';
      case 'credit': return 'warning';
      case 'advance': return 'info';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container fluid className="py-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading proforma invoice details...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid className="py-4">
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/proforma')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Proforma Invoices
        </Button>
      </Container>
    );
  }

  if (!proformaInvoice) {
    return (
      <Container fluid className="py-4">
        <Alert variant="warning">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          Proforma invoice not found
        </Alert>
        <Button variant="outline-primary" onClick={() => navigate('/procurement/proforma')}>
          <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
          Back to Proforma Invoices
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Button 
            variant="outline-primary" 
            onClick={() => navigate('/procurement/proforma')}
            className="mb-2"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to Proforma Invoices
          </Button>
          <h2 className="mb-1">
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2 text-primary" />
            Proforma Invoice Details
          </h2>
          <p className="text-muted mb-0">View proforma invoice information and items</p>
        </div>
        <div>
          <Badge bg={getStatusBadgeVariant(proformaInvoice.status)} className="fs-6 me-2">
            {proformaInvoice.status?.toUpperCase()}
          </Badge>
          <Badge bg={getPaymentTermsBadgeVariant(proformaInvoice.payment_terms)} className="fs-6">
            {proformaInvoice.payment_terms?.toUpperCase()}
          </Badge>
        </div>
      </div>

      <Row>
        {/* Proforma Invoice Information */}
        <Col lg={8} className="mb-4">
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
                Invoice Information
              </h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Proforma Number</label>
                    <div className="fw-semibold">{proformaInvoice.proforma_number}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Invoice Date
                    </label>
                    <div>{new Date(proformaInvoice.invoice_date).toLocaleDateString()}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faClock} className="me-1" />
                      Valid Until
                    </label>
                    <div>
                      {proformaInvoice.valid_until 
                        ? new Date(proformaInvoice.valid_until).toLocaleDateString()
                        : 'Not specified'
                      }
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Payment Terms</label>
                    <div>
                      <Badge bg={getPaymentTermsBadgeVariant(proformaInvoice.payment_terms)}>
                        {proformaInvoice.payment_terms?.toUpperCase()}
                      </Badge>
                      {proformaInvoice.credit_period_days && (
                        <span className="ms-2 text-muted">
                          ({proformaInvoice.credit_period_days} days)
                        </span>
                      )}
                    </div>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="mb-3">
                    <label className="form-label text-muted">Status</label>
                    <div>
                      <Badge bg={getStatusBadgeVariant(proformaInvoice.status)}>
                        {proformaInvoice.status?.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faBuilding} className="me-1" />
                      To Tenant
                    </label>
                    <div>{proformaInvoice.to_tenant_name || 'N/A'}</div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">
                      <FontAwesomeIcon icon={faCalendar} className="me-1" />
                      Payment Due Date
                    </label>
                    <div>
                      {proformaInvoice.payment_due_date 
                        ? new Date(proformaInvoice.payment_due_date).toLocaleDateString()
                        : 'Not specified'
                      }
                    </div>
                  </div>
                  <div className="mb-3">
                    <label className="form-label text-muted">Total Amount</label>
                    <div className="fw-semibold text-success fs-5">
                      ₹{parseFloat(proformaInvoice.total_amount || 0).toLocaleString()}
                    </div>
                  </div>
                </Col>
              </Row>
              
              {proformaInvoice.notes && (
                <div className="mt-3">
                  <label className="form-label text-muted">Notes</label>
                  <div className="bg-light p-3 rounded">{proformaInvoice.notes}</div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Summary Card */}
        <Col lg={4} className="mb-4">
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Payment Summary
              </h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Items:</span>
                <span className="fw-semibold">{proformaInvoice.items?.length || 0}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Total Quantity:</span>
                <span className="fw-semibold">
                  {proformaInvoice.items?.reduce((sum, item) => sum + (item.quantity || 0), 0) || 0}
                </span>
              </div>
              <hr />
              <div className="d-flex justify-content-between mb-2">
                <span>Subtotal:</span>
                <span>₹{parseFloat(proformaInvoice.subtotal || 0).toLocaleString()}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Tax ({proformaInvoice.tax_rate || 0}%):</span>
                <span>₹{parseFloat(proformaInvoice.tax_amount || 0).toLocaleString()}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span className="fw-semibold">Total:</span>
                <span className="fw-semibold">₹{parseFloat(proformaInvoice.total_amount || 0).toLocaleString()}</span>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>Paid:</span>
                <span className="text-success">₹{parseFloat(proformaInvoice.paid_amount || 0).toLocaleString()}</span>
              </div>
              <div className="d-flex justify-content-between">
                <span className="fw-semibold">Balance:</span>
                <span className="fw-semibold text-warning">
                  ₹{parseFloat(proformaInvoice.balance_amount || 0).toLocaleString()}
                </span>
              </div>
              
              {proformaInvoice.created_at && (
                <div className="mt-3 pt-3 border-top">
                  <small className="text-muted">
                    <FontAwesomeIcon icon={faUser} className="me-1" />
                    Created: {new Date(proformaInvoice.created_at).toLocaleString()}
                  </small>
                  {proformaInvoice.created_by_username && (
                    <div className="mt-1">
                      <small className="text-muted">
                        By: {proformaInvoice.created_by_username}
                      </small>
                    </div>
                  )}
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Invoice Items */}
      <Card>
        <Card.Header>
          <h5 className="mb-0">
            <FontAwesomeIcon icon={faBoxes} className="me-2" />
            Invoice Items
          </h5>
        </Card.Header>
        <Card.Body className="p-0">
          {proformaInvoice.items && proformaInvoice.items.length > 0 ? (
            <div className="table-responsive">
              <Table striped hover className="mb-0">
                <thead className="table-dark">
                  <tr>
                    <th>Item Name</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit</th>
                    <th>Unit Price</th>
                    <th>Total Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {proformaInvoice.items.map((item, index) => (
                    <tr key={item.id || index}>
                      <td className="fw-semibold">{item.item_name}</td>
                      <td>{item.description || '-'}</td>
                      <td>{item.quantity}</td>
                      <td>{item.unit}</td>
                      <td>₹{parseFloat(item.unit_price || 0).toFixed(2)}</td>
                      <td className="fw-semibold">₹{parseFloat(item.total_amount || 0).toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faBoxes} className="text-muted fa-2x mb-3" />
              <p className="text-muted">No items found for this proforma invoice</p>
            </div>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default ProformaInvoiceDetail;
