"""
Database-based Inventory Management Routes
Provides storeroom-wise inventory management with tenant-based access control
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
from database_manager import db_manager
from utils import token_required, require_module_access, require_role

inventory_db_bp = Blueprint('inventory_db', __name__)

@inventory_db_bp.route('/api/inventory-db', methods=['GET'])
@token_required
@require_module_access('INVENTORY')
def get_inventory_items():
    """Get inventory items with storeroom-wise organization and tenant-based filtering"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Get query parameters
        storeroom_id = request.args.get('storeroom_id', type=int)
        category = request.args.get('category')
        low_stock = request.args.get('low_stock', 'false').lower() == 'true'
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        # Base query with storeroom and tenant information
        query = """
            SELECT i.*, 
                   s.name as storeroom_name, 
                   s.storeroom_id as storeroom_code,
                   t.name as tenant_name, 
                   t.site_code,
                   t.is_hub,
                   CASE WHEN i.quantity <= i.reorder_level THEN 1 ELSE 0 END as is_low_stock
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            LEFT JOIN tenants t ON i.tenant_id = t.id
            WHERE 1=1
        """
        params = []
        
        # Apply tenant-based filtering
        if user_role in ['admin', 'hub_admin']:
            # Mayiladuthurai Hub users can see ALL inventory across all storerooms
            pass
        else:
            # Franchise users can only see their own inventory
            query += " AND i.tenant_id = ?"
            params.append(user_tenant_id)
        
        # Filter by active status
        if active_only:
            query += " AND i.is_active = 1"
        
        # Filter by storeroom
        if storeroom_id:
            query += " AND i.storeroom_id = ?"
            params.append(storeroom_id)
        
        # Filter by category
        if category:
            query += " AND i.category = ?"
            params.append(category)
        
        # Filter by low stock
        if low_stock:
            query += " AND i.quantity <= i.reorder_level"
        
        query += " ORDER BY t.name, s.name, i.category, i.name"
        
        inventory_items = db_manager.execute_query(query, tuple(params))
        
        # Group by storeroom for better organization
        storeroom_inventory = {}
        for item in inventory_items:
            storeroom_key = f"{item['tenant_name']} - {item['storeroom_name']}"
            if storeroom_key not in storeroom_inventory:
                storeroom_inventory[storeroom_key] = {
                    'storeroom_id': item['storeroom_id'],
                    'storeroom_name': item['storeroom_name'],
                    'storeroom_code': item['storeroom_code'],
                    'tenant_id': item['tenant_id'],
                    'tenant_name': item['tenant_name'],
                    'site_code': item['site_code'],
                    'is_hub': item['is_hub'],
                    'items': []
                }
            storeroom_inventory[storeroom_key]['items'].append(item)
        
        return jsonify({
            'success': True,
            'data': {
                'storeroom_inventory': storeroom_inventory,
                'total_items': len(inventory_items),
                'low_stock_count': sum(1 for item in inventory_items if item['is_low_stock'])
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db/<int:item_id>', methods=['GET'])
@token_required
@require_module_access('INVENTORY')
def get_inventory_item(item_id):
    """Get a specific inventory item with full details"""
    try:
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        query = """
            SELECT i.*, 
                   s.name as storeroom_name, 
                   s.storeroom_id as storeroom_code,
                   t.name as tenant_name, 
                   t.site_code,
                   u.username as created_by_username
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            LEFT JOIN tenants t ON i.tenant_id = t.id
            LEFT JOIN users u ON i.created_by = u.id
            WHERE i.id = ?
        """
        
        items = db_manager.execute_query(query, (item_id,))
        if not items:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404
        
        item = items[0]
        
        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if item['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        # Get recent transactions for this item
        transactions_query = """
            SELECT it.*, u.username as created_by_username
            FROM inventory_transactions it
            LEFT JOIN users u ON it.created_by = u.id
            WHERE it.inventory_id = ?
            ORDER BY it.created_at DESC
            LIMIT 10
        """
        transactions = db_manager.execute_query(transactions_query, (item_id,))
        
        return jsonify({
            'success': True,
            'data': {
                'item': item,
                'recent_transactions': transactions
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db', methods=['POST'])
@token_required
@require_module_access('INVENTORY')
def create_inventory_item():
    """Create a new inventory item"""
    try:
        data = request.get_json()
        user = request.current_user
        user_tenant_id = user.get('tenant_id')
        
        # Validate required fields
        required_fields = ['name', 'sku', 'category', 'quantity', 'unit', 'reorder_level', 'storeroom_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        # Check if SKU already exists
        existing = db_manager.execute_query(
            "SELECT id FROM inventory WHERE sku = ?", (data['sku'],)
        )
        if existing:
            return jsonify({
                'success': False,
                'error': 'SKU already exists'
            }), 400
        
        # Verify storeroom exists and user has access
        storeroom_query = """
            SELECT s.*, t.name as tenant_name 
            FROM storerooms s 
            LEFT JOIN tenants t ON s.tenant_id = t.id 
            WHERE s.id = ?
        """
        storerooms = db_manager.execute_query(storeroom_query, (data['storeroom_id'],))
        if not storerooms:
            return jsonify({
                'success': False,
                'error': 'Storeroom not found'
            }), 404
        
        storeroom = storerooms[0]
        
        # Check tenant access for storeroom
        if user.get('role') not in ['admin', 'hub_admin']:
            if storeroom['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied to this storeroom'
                }), 403
        
        # Create inventory item
        insert_query = """
            INSERT INTO inventory (
                name, sku, category, description, quantity, unit, reorder_level,
                cost_price, selling_price, supplier, location, expiry_date,
                storeroom_id, tenant_id, batch_number, barcode, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            data['name'],
            data['sku'],
            data['category'],
            data.get('description', ''),
            int(data['quantity']),
            data['unit'],
            int(data['reorder_level']),
            float(data.get('cost_price', 0)),
            float(data.get('selling_price', 0)),
            data.get('supplier', ''),
            data.get('location', ''),
            data.get('expiry_date'),
            data['storeroom_id'],
            storeroom['tenant_id'],  # Use storeroom's tenant_id
            data.get('batch_number', ''),
            data.get('barcode', ''),
            user.get('id')
        )
        
        result = db_manager.execute_query(insert_query, params)
        item_id = result
        
        # Create initial inventory transaction
        if int(data['quantity']) > 0:
            transaction_query = """
                INSERT INTO inventory_transactions (
                    inventory_id, transaction_type, quantity, reference_type, 
                    reason, unit_cost, total_cost, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            total_cost = float(data.get('cost_price', 0)) * int(data['quantity'])
            db_manager.execute_query(transaction_query, (
                item_id, 'in', int(data['quantity']), 'manual_adjustment',
                'Initial stock entry', float(data.get('cost_price', 0)), total_cost, user.get('id')
            ))
        
        return jsonify({
            'success': True,
            'message': 'Inventory item created successfully',
            'item_id': item_id
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@inventory_db_bp.route('/api/inventory-db/<int:item_id>/transaction', methods=['POST'])
@token_required
@require_module_access('INVENTORY')
def add_inventory_transaction(item_id):
    """Add a stock transaction (in/out/adjustment)"""
    try:
        data = request.get_json()
        user = request.current_user
        user_role = user.get('role')
        user_tenant_id = user.get('tenant_id')
        
        # Validate required fields
        required_fields = ['transaction_type', 'quantity', 'reason']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        # Get inventory item with access check
        item_query = """
            SELECT i.*, s.tenant_id as storeroom_tenant_id
            FROM inventory i
            LEFT JOIN storerooms s ON i.storeroom_id = s.id
            WHERE i.id = ? AND i.is_active = 1
        """
        items = db_manager.execute_query(item_query, (item_id,))
        if not items:
            return jsonify({
                'success': False,
                'error': 'Inventory item not found'
            }), 404
        
        item = items[0]
        
        # Check tenant access
        if user_role not in ['admin', 'hub_admin']:
            if item['tenant_id'] != user_tenant_id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        transaction_type = data['transaction_type']
        quantity = int(data['quantity'])
        
        # Validate transaction type
        if transaction_type not in ['in', 'out', 'adjustment']:
            return jsonify({
                'success': False,
                'error': 'Invalid transaction type'
            }), 400
        
        # Check stock availability for 'out' transactions
        if transaction_type == 'out' and item['quantity'] < quantity:
            return jsonify({
                'success': False,
                'error': f'Insufficient stock. Available: {item["quantity"]}, Requested: {quantity}'
            }), 400
        
        # Calculate new quantity
        if transaction_type == 'in':
            new_quantity = item['quantity'] + quantity
        elif transaction_type == 'out':
            new_quantity = item['quantity'] - quantity
        else:  # adjustment
            new_quantity = quantity
        
        # Update inventory quantity
        update_query = "UPDATE inventory SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        db_manager.execute_query(update_query, (new_quantity, item_id))
        
        # Create transaction record
        transaction_query = """
            INSERT INTO inventory_transactions (
                inventory_id, transaction_type, quantity, reference_type, reference_id,
                reason, notes, unit_cost, total_cost, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        unit_cost = float(data.get('unit_cost', item.get('cost_price', 0)))
        total_cost = unit_cost * quantity
        
        db_manager.execute_query(transaction_query, (
            item_id,
            transaction_type,
            quantity,
            data.get('reference_type', 'manual_adjustment'),
            data.get('reference_id'),
            data['reason'],
            data.get('notes', ''),
            unit_cost,
            total_cost,
            user.get('id')
        ))
        
        return jsonify({
            'success': True,
            'message': 'Transaction completed successfully',
            'new_quantity': new_quantity,
            'previous_quantity': item['quantity']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
